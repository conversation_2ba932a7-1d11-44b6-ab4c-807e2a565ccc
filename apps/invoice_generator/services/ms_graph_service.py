import requests
import json
from typing import Dict, Any, Optional
from utils.logger import get_logger

logger = get_logger("invoice_generator.ms_graph_service")


class MSGraphService:
    """Service for interacting with Microsoft Graph API for email operations."""

    BASE_URL = "https://graph.microsoft.com/v1.0"

    def __init__(self, access_token: str):
        """Initialize the service with an access token."""
        self.access_token = access_token
        self.headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

    def get_user_profile(self) -> Optional[Dict[str, Any]]:
        """Get the current user's profile information."""
        try:
            url = f"{self.BASE_URL}/me"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()

            user_data = response.json()
            logger.info(
                f"Successfully retrieved user profile for {user_data.get('userPrincipalName')}"
            )
            return user_data

        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting user profile: {str(e)}")
            return None

    def send_email(
        self,
        to_email: str,
        subject: str,
        body: str,
        attachments: Optional[list] = None,
        from_email: Optional[str] = None,
    ) -> bool:
        """
        Send an email using Microsoft Graph API.

        Args:
            to_email: Recipient email address
            subject: Email subject
            body: Email body (HTML or plain text)
            attachments: List of attachment data (optional)
            from_email: Sender email (optional, uses authenticated user if not provided)

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            # Prepare email message
            message = {
                "subject": subject,
                "body": {"contentType": "HTML", "content": body},
                "toRecipients": [{"emailAddress": {"address": to_email}}],
            }

            # Add attachments if provided
            if attachments:
                message["attachments"] = attachments

            # Prepare the request payload
            email_data = {"message": message, "saveToSentItems": True}

            # Send the email
            url = f"{self.BASE_URL}/me/sendMail"
            response = requests.post(
                url, headers=self.headers, data=json.dumps(email_data)
            )
            response.raise_for_status()

            logger.info(
                f"Successfully sent email to {to_email} with subject '{subject}'"
            )
            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"Error sending email: {str(e)}")
            if hasattr(e, "response") and e.response is not None:
                logger.error(f"Response content: {e.response.text}")
            return False

    def send_test_email(self, to_email: str, from_email: Optional[str] = None) -> bool:
        """
        Send a test email to verify the configuration.

        Args:
            to_email: Email address to send test email to

        Returns:
            bool: True if test email was sent successfully, False otherwise
        """
        subject = "MizuFlow - Test Email Configuration"
        body = """
        <html>
        <body>
            <h2>Email Configuration Test</h2>
            <p>This is a test email from MizuFlow to verify your Microsoft Outlook email configuration.</p>
            <p>If you received this email, your email settings are working correctly!</p>
            <br>
            <p>Best regards,<br>
            The MizuFlow Team</p>
        </body>
        </html>
        """

        return self.send_email(to_email, subject, body, from_email=from_email)

    def validate_token(self) -> bool:
        """
        Validate if the access token is still valid.

        Returns:
            bool: True if token is valid, False otherwise
        """
        try:
            url = f"{self.BASE_URL}/me"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"Token validation failed: {str(e)}")
            return False

    @staticmethod
    def create_attachment_from_file(
        file_path: str, file_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        Create an attachment object from a file path.

        Args:
            file_path: Path to the file
            file_name: Name of the file

        Returns:
            Optional[Dict] containing attachment data for Graph API or None if creation fails
        """
        import base64
        import mimetypes

        try:
            # Read file content
            with open(file_path, "rb") as file:
                file_content = file.read()

            # Encode to base64
            encoded_content = base64.b64encode(file_content).decode("utf-8")

            # Determine MIME type
            mime_type, _ = mimetypes.guess_type(file_path)
            if not mime_type:
                mime_type = "application/octet-stream"

            return {
                "@odata.type": "#microsoft.graph.fileAttachment",
                "name": file_name,
                "contentType": mime_type,
                "contentBytes": encoded_content,
            }

        except Exception as e:
            logger.error(f"Error creating attachment from file {file_path}: {str(e)}")
            return None
