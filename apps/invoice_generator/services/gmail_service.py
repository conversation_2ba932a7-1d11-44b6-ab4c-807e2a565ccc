import base64
import json
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Optional, Dict, Any, List
from utils.logger import get_logger

logger = get_logger("invoice_generator.gmail_service")


class GmailService:
    """Service class for interacting with Gmail API."""

    def __init__(self, access_token: str):
        """
        Initialize Gmail service with access token.
        
        Args:
            access_token: OAuth2 access token for Gmail API
        """
        self.access_token = access_token
        self.BASE_URL = "https://gmail.googleapis.com/gmail/v1"
        self.headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

    def get_user_profile(self) -> Optional[Dict[str, Any]]:
        """Get the current user's profile information."""
        try:
            # Use Google's userinfo endpoint instead of Gmail API for profile
            url = "https://www.googleapis.com/oauth2/v2/userinfo"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()

            user_data = response.json()
            logger.info(
                f"Successfully retrieved user profile for {user_data.get('email')}"
            )
            return user_data

        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting user profile: {str(e)}")
            return None

    def send_email(
        self,
        to_email: str,
        subject: str,
        body: str,
        attachments: Optional[List[Dict[str, Any]]] = None,
        from_email: Optional[str] = None,
    ) -> bool:
        """
        Send an email using Gmail API.

        Args:
            to_email: Recipient email address
            subject: Email subject
            body: Email body (HTML or plain text)
            attachments: List of attachment data (optional)
            from_email: Sender email (optional, uses authenticated user if not provided)

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            # Create the email message
            message = self._create_message(
                to_email=to_email,
                subject=subject,
                body=body,
                attachments=attachments,
                from_email=from_email
            )

            # Send the email
            url = f"{self.BASE_URL}/users/me/messages/send"
            email_data = {"raw": message}
            
            response = requests.post(
                url, 
                headers=self.headers, 
                data=json.dumps(email_data)
            )
            response.raise_for_status()

            logger.info(
                f"Successfully sent email to {to_email} with subject '{subject}'"
            )
            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"Error sending email: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response content: {e.response.text}")
            return False

    def _create_message(
        self,
        to_email: str,
        subject: str,
        body: str,
        attachments: Optional[List[Dict[str, Any]]] = None,
        from_email: Optional[str] = None,
    ) -> str:
        """
        Create a base64url encoded email message.

        Args:
            to_email: Recipient email address
            subject: Email subject
            body: Email body (HTML or plain text)
            attachments: List of attachment data (optional)
            from_email: Sender email (optional)

        Returns:
            str: Base64url encoded email message
        """
        # Create message container
        if attachments:
            message = MIMEMultipart()
        else:
            message = MIMEText(body, 'html')
            message['to'] = to_email
            message['subject'] = subject
            if from_email:
                message['from'] = from_email
            
            # Encode and return
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode('utf-8')
            return raw_message

        # For messages with attachments
        message['to'] = to_email
        message['subject'] = subject
        if from_email:
            message['from'] = from_email

        # Add body
        body_part = MIMEText(body, 'html')
        message.attach(body_part)

        # Add attachments
        for attachment in attachments:
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(attachment['content'])
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {attachment["filename"]}'
            )
            message.attach(part)

        # Encode and return
        raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode('utf-8')
        return raw_message

    def send_test_email(self, to_email: str, from_email: str) -> bool:
        """
        Send a test email to verify the configuration.

        Args:
            to_email: Recipient email address
            from_email: Sender email address

        Returns:
            bool: True if test email was sent successfully, False otherwise
        """
        subject = "Test Email from MizuFlow Invoice Generator"
        body = """
        <html>
        <body>
            <h2>Test Email Successful!</h2>
            <p>This is a test email from MizuFlow Invoice Generator to verify your Gmail configuration.</p>
            <p>Your Gmail account has been successfully connected and is ready to send invoices.</p>
            <br>
            <p>Best regards,<br>
            MizuFlow Team</p>
        </body>
        </html>
        """

        return self.send_email(
            to_email=to_email,
            subject=subject,
            body=body,
            from_email=from_email
        )

    def validate_token(self) -> bool:
        """
        Validate if the access token is still valid.

        Returns:
            bool: True if token is valid, False otherwise
        """
        try:
            url = "https://www.googleapis.com/oauth2/v2/userinfo"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"Token validation failed: {str(e)}")
            return False

    @staticmethod
    def create_attachment_from_file(
        file_path: str, file_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        Create an attachment object from a file path.

        Args:
            file_path: Path to the file
            file_name: Name of the file

        Returns:
            Dict containing attachment data or None if failed
        """
        try:
            with open(file_path, 'rb') as file:
                content = file.read()
                
            return {
                'filename': file_name,
                'content': content
            }

        except Exception as e:
            logger.error(f"Error creating attachment from file {file_path}: {str(e)}")
            return None

    @staticmethod
    def create_attachment_from_bytes(
        content: bytes, file_name: str
    ) -> Dict[str, Any]:
        """
        Create an attachment object from bytes content.

        Args:
            content: File content as bytes
            file_name: Name of the file

        Returns:
            Dict containing attachment data
        """
        return {
            'filename': file_name,
            'content': content
        }
