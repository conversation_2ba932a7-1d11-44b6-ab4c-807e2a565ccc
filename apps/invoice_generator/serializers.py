from rest_framework import serializers
from .models import CompanyTemplate, SalesInvoiceData, EmailProviderConfig


class CompanyTemplateSerializer(serializers.ModelSerializer):
    """Serializer for CompanyTemplate model."""

    class Meta:
        model = CompanyTemplate
        fields = [
            "id",
            "template_name",
            "template_id",
            "template_display_name",
            "company_name",
            "address_line_1",
            "address_line_2",
            "city",
            "state_province",
            "postal_code",
            "country",
            "phone",
            "email",
            "website",
            "default_payment_terms",
            "bank_name",
            "account_number",
            "routing_number",
            "swift_code",
            "tax_id",
            "business_registration",
            "logo_url",
            "created_at",
            "updated_at",
            "last_used",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def create(self, validated_data):
        # Set the user from the request context
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)


class CompanyTemplateListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing company templates."""

    class Meta:
        model = CompanyTemplate
        fields = [
            "id",
            "template_name",
            "template_display_name",
            "company_name",
            "created_at",
            "updated_at",
            "last_used",
        ]


class SalesInvoiceDataSerializer(serializers.ModelSerializer):
    """Serializer for SalesInvoiceData model."""

    class Meta:
        model = SalesInvoiceData
        fields = [
            "id",
            "row_number",
            "invoice_data",
            "is_processed",
            "processing_error",
            "generated_invoice_path",
            "invoice_number",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class ColumnMappingRequestSerializer(serializers.Serializer):
    """Serializer for column mapping requests."""

    csv_columns = serializers.ListField(
        child=serializers.CharField(max_length=200),
        help_text="List of column names from the uploaded CSV file",
    )

    def validate_csv_columns(self, value):
        if not value:
            raise serializers.ValidationError("CSV columns list cannot be empty")
        return value


class ColumnMappingResponseSerializer(serializers.Serializer):
    """Serializer for column mapping responses."""

    suggested_mappings = serializers.DictField(
        child=serializers.CharField(max_length=200),
        help_text="Suggested mappings from invoice fields to CSV columns",
    )
    confidence_scores = serializers.DictField(
        child=serializers.FloatField(min_value=0.0, max_value=1.0),
        help_text="Confidence scores for each suggested mapping",
    )


class EmailProviderConfigSerializer(serializers.ModelSerializer):
    """Serializer for EmailProviderConfig model."""

    class Meta:
        model = EmailProviderConfig
        fields = [
            "id",
            "provider",
            "account_info",
            "is_active",
            "created_at",
            "updated_at",
            "last_used",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def create(self, validated_data):
        # Set the user from the request context
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)
