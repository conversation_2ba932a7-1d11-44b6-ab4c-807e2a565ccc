import { API_ENDPOINTS } from "@/config/api";
import { gmailConfig } from "@/lib/gmail/config";
import api from "@/services/api";
import { GoogleOAuthProvider, useGoogleLogin } from "@react-oauth/google";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { TbArrowLeft, TbCheck, TbLoader, TbMail, TbPlus, TbSend, TbTrash } from "react-icons/tb";

const GmailAuthContent = ({ onBack, variants }) => {
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [loading, setLoading] = useState(false);
  const [sendingTest, setSendingTest] = useState(false);
  const [currentStep, setCurrentStep] = useState('account_selection'); // account_selection, test
  const [existingConfigs, setExistingConfigs] = useState([]);
  const [isLoadingConfigs, setIsLoadingConfigs] = useState(true);
  const [recipientEmail, setRecipientEmail] = useState('');
  const [emailError, setEmailError] = useState('');

  // Fetch existing email configurations when component mounts
  useEffect(() => {
    const fetchEmailConfigs = async () => {
      try {
        const response = await api.get(`${API_ENDPOINTS.MS_GRAPH_ACCOUNTS}?provider=gmail`);
        const data = response.data;
        
        if (data.success) {
          setExistingConfigs(data.accounts);
        } else {
          toast.error('Failed to fetch Gmail configurations');
        }
      } catch (error) {
        console.error('Error fetching Gmail configs:', error);
        toast.error('Failed to fetch Gmail configurations');
      } finally {
        setIsLoadingConfigs(false);
      }
    };

    fetchEmailConfigs();
  }, []);

  const login = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      setLoading(true);
      try {
        // Get user info using the access token
        const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
          headers: {
            Authorization: `Bearer ${tokenResponse.access_token}`,
          },
        });
        
        if (!userInfoResponse.ok) {
          throw new Error('Failed to get user information');
        }
        
        const userInfo = await userInfoResponse.json();
        
        // Create account object similar to MSAL format
        const account = {
          name: userInfo.name,
          username: userInfo.email,
          email: userInfo.email,
          id: userInfo.id,
          picture: userInfo.picture,
          accessToken: tokenResponse.access_token
        };

        setSelectedAccount(account);
        setCurrentStep('test');
        toast.success('Successfully connected to Gmail!');
      } catch (error) {
        console.error('Login failed:', error);
        toast.error('Failed to connect to Gmail. Please try again.');
      } finally {
        setLoading(false);
      }
    },
    onError: (error) => {
      console.error('Login failed:', error);
      toast.error('Failed to connect to Gmail. Please try again.');
      setLoading(false);
    },
    scope: gmailConfig.scope,
    flow: 'auth-code',
  });

  const handleAddNewAccount = () => {
    setLoading(true);
    login();
  };

  const handleRemoveConfig = async (configId) => {
    try {
      const response = await api.delete(`${API_ENDPOINTS.MS_GRAPH_SAVE_CONFIG}/${configId}`);
      if (response.data.success) {
        setExistingConfigs(prev => prev.filter(config => config.id !== configId));
        toast.success('Email configuration removed successfully');
      } else {
        throw new Error(response.data.error || 'Failed to remove configuration');
      }
    } catch (error) {
      console.error('Failed to remove config:', error);
      toast.error('Failed to remove email configuration');
    }
  };

  const handleConnectedAccountClick = async (config) => {
    // For Gmail, we'll need to re-authenticate since we don't store refresh tokens
    // This is a security best practice
    toast.info('Please re-authenticate to use this Gmail account');
    handleAddNewAccount();
  };

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSendTestEmail = async () => {
    if (!selectedAccount) {
      toast.error('Please select an account first');
      return;
    }

    if (!recipientEmail) {
      setEmailError('Recipient email is required');
      return;
    }

    if (!validateEmail(recipientEmail)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    setEmailError('');
    setSendingTest(true);
    try {
      // Send test email via backend
      const response = await api.post(API_ENDPOINTS.MS_GRAPH_SEND_TEST_EMAIL, {
        access_token: selectedAccount.accessToken,
        from_email: selectedAccount.email,
        to_email: recipientEmail,
        account_info: {
          name: selectedAccount.name,
          username: selectedAccount.email,
          email: selectedAccount.email,
          id: selectedAccount.id,
          picture: selectedAccount.picture
        },
        provider: 'gmail'
      });

      if (response.data.success) {
        toast.success('Test email sent successfully!');
        
        // Save the configuration
        const saveResponse = await api.post(API_ENDPOINTS.MS_GRAPH_SAVE_CONFIG, {
          account_info: {
            name: selectedAccount.name,
            username: selectedAccount.email,
            email: selectedAccount.email,
            id: selectedAccount.id,
            picture: selectedAccount.picture
          },
          provider: 'gmail'
        });
        
        if (saveResponse.data.success) {
          // Add the new config to the list
          setExistingConfigs(prev => [...prev, saveResponse.data.config]);
          toast.success('Email configuration saved!');
        }
      } else {
        throw new Error(response.data.error || 'Failed to send test email');
      }
    } catch (error) {
      console.error('Test email failed:', error);
      toast.error(`Failed to send test email: ${error.message}`);
    } finally {
      setSendingTest(false);
    }
  };

  const renderAccountSelectionStep = () => (
    <>
      {/* Existing Configurations */}
      {isLoadingConfigs ? (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-center">
            <TbLoader className="h-6 w-6 animate-spin text-red-600" />
            <span className="ml-2 text-gray-600">Loading Gmail configurations...</span>
          </div>
        </div>
      ) : (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-6">
            <TbMail className="h-6 w-6 text-red-600" />
            <h3 className="text-lg font-medium text-gray-900">
              Connected Gmail Accounts
            </h3>
          </div>

          {/* Connected Accounts List */}
          {existingConfigs.length > 0 && (
            <div className="space-y-4 mb-6">
              {existingConfigs.map((config) => (
                <div
                  key={config.id}
                  className="p-4 border border-gray-200 rounded-lg"
                >
                  <div className="flex items-center justify-between">
                    <div 
                      className="flex items-center gap-4 flex-grow cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
                      onClick={() => handleConnectedAccountClick(config)}
                    >
                      <div className="text-3xl">✉️</div>
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {config.account_info.name || config.account_info.email}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {config.account_info.email}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-sm text-green-600 bg-green-50 px-2 py-1 rounded-full flex items-center gap-1">
                        <TbCheck className="h-4 w-4" />
                        Connected
                      </span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveConfig(config.id);
                        }}
                        className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                        title="Remove configuration"
                      >
                        <TbTrash className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Add New Account Button */}
          <button
            onClick={handleAddNewAccount}
            disabled={loading}
            className="w-full flex items-center justify-center gap-2 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-red-400 hover:bg-red-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <TbLoader className="h-5 w-5 animate-spin" />
                Connecting...
              </>
            ) : (
              <>
                <TbPlus className="h-5 w-5 text-red-600" />
                <span className="text-red-600 font-medium">Add New Gmail Account</span>
              </>
            )}
          </button>

          {existingConfigs.length === 0 && !loading && (
            <p className="text-sm text-gray-600 text-center mt-4">
              No Gmail accounts connected yet. Add your first Gmail account to start sending invoices.
            </p>
          )}
        </div>
      )}
    </>
  );

  const renderTestStep = () => (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Test Email Configuration
      </h3>
      
      {selectedAccount && (
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <p className="text-sm text-gray-600 mb-1">Sending from:</p>
          <p className="font-medium text-gray-900">{selectedAccount.name}</p>
          <p className="text-sm text-gray-600">{selectedAccount.email}</p>
        </div>
      )}

      <div className="space-y-6">
        <div>
          <label htmlFor="recipientEmail" className="block text-sm font-medium text-gray-700">
            Recipient Email <span className="text-red-500">*</span>
          </label>
          <div className="mt-2">
            <input
              type="email"
              id="recipientEmail"
              name="recipientEmail"
              value={recipientEmail}
              onChange={(e) => {
                setRecipientEmail(e.target.value);
                setEmailError('');
              }}
              placeholder="Enter recipient's email address"
              className={`
                block w-full px-4 py-2.5 text-gray-900 placeholder:text-gray-500
                border rounded-lg shadow-sm
                focus:outline-none focus:ring-2 focus:ring-offset-0
                disabled:opacity-50 disabled:cursor-not-allowed
                ${emailError 
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20' 
                  : 'border-gray-300 focus:border-red-500 focus:ring-red-500/20'
                }
              `}
              disabled={sendingTest}
            />
            {emailError && (
              <p className="mt-2 text-sm text-red-600">{emailError}</p>
            )}
          </div>
        </div>

        <div className="text-center pt-4">
          <p className="text-gray-600 mb-6">
            Send a test email to verify your configuration
          </p>
          
          <button
            onClick={handleSendTestEmail}
            disabled={sendingTest}
            className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mx-auto"
          >
            {sendingTest ? (
              <>
                <TbLoader className="h-5 w-5 animate-spin" />
                Sending Test Email...
              </>
            ) : (
              <>
                <TbSend className="h-5 w-5" />
                Send Test Email
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <motion.button
            onClick={onBack}
            className="w-12 h-12 rounded-full flex items-center justify-center bg-red-600 text-white shadow-md hover:bg-red-700 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Go back"
          >
            <TbArrowLeft className="h-6 w-6" />
          </motion.button>
          <div>
            <h2 className="text-xl font-medium text-gray-700">
              Gmail Setup
            </h2>
            <p className="text-gray-600 mt-1">
              Configure your Gmail account for sending invoices
            </p>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className={`flex items-center gap-2 ${currentStep === 'account_selection' ? 'text-red-600' : currentStep === 'test' ? 'text-green-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'account_selection' ? 'bg-red-100' : currentStep === 'test' ? 'bg-green-100' : 'bg-gray-100'}`}>
              {currentStep === 'test' ? <TbCheck className="h-5 w-5" /> : '1'}
            </div>
            <span className="text-sm font-medium">Select Account</span>
          </div>
          <div className={`flex items-center gap-2 ${currentStep === 'test' ? 'text-red-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'test' ? 'bg-red-100' : 'bg-gray-100'}`}>
              2
            </div>
            <span className="text-sm font-medium">Test Email</span>
          </div>
        </div>
      </div>

      {/* Step Content */}
      {currentStep === 'account_selection' && renderAccountSelectionStep()}
      {currentStep === 'test' && renderTestStep()}
    </motion.div>
  );
};

const GmailAuth = ({ onBack, variants }) => {
  return (
    <GoogleOAuthProvider clientId={gmailConfig.clientId}>
      <GmailAuthContent onBack={onBack} variants={variants} />
    </GoogleOAuthProvider>
  );
};

export default GmailAuth;
