// app/dashboard/page.js
'use client';

import React from 'react';
import AppLayout from '@/components/AppLayout';
import { useAuth } from '@/context/AuthContext';
import PrivateRoute from '@/components/auth/PrivateRoute';

function DashboardContent({ user }) {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-800 mb-2">
          Welcome, {user?.first_name || 'User'}!
        </h1>
        <p className="text-gray-600">
          You are logged in as {user?.email}. You can manage your account and access all features.
        </p>
      </div>
      
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Dashboard widgets */}
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
          <h3 className="font-medium text-blue-800 mb-2">Quick Actions</h3>
          <p className="text-sm text-blue-700">Access common tasks and features</p>
        </div>
        
        <div className="bg-green-50 p-6 rounded-lg border border-green-100">
          <h3 className="font-medium text-green-800 mb-2">Recent Activities</h3>
          <p className="text-sm text-green-700">View your recent activities</p>
        </div>
        
        <div className="bg-purple-50 p-6 rounded-lg border border-purple-100">
          <h3 className="font-medium text-purple-800 mb-2">Account Status</h3>
          <p className="text-sm text-purple-700">View your account status and details</p>
        </div>
      </div>
    </div>
  );
}

export default function DashboardPage() {
  const { user } = useAuth();

  return (
      <PrivateRoute>
        <AppLayout>
          <DashboardContent user={user} />
        </AppLayout>
      </PrivateRoute>
  );
}
