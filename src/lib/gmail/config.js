// lib/gmail/config.js

export const gmailConfig = {
  clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
  redirectUri: typeof window !== 'undefined'
    ? process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI || `${window.location.origin}/auth/gmail/callback`
    : process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI,
  scope: [
    'https://www.googleapis.com/auth/gmail.send',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/userinfo.profile'
  ].join(' '),
  responseType: 'code',
  accessType: 'offline',
  prompt: 'consent'
};

// Gmail API scopes
export const gmailScopes = {
  SEND: 'https://www.googleapis.com/auth/gmail.send',
  READ: 'https://www.googleapis.com/auth/gmail.readonly',
  MODIFY: 'https://www.googleapis.com/auth/gmail.modify',
  COMPOSE: 'https://www.googleapis.com/auth/gmail.compose',
  USER_EMAIL: 'https://www.googleapis.com/auth/userinfo.email',
  USER_PROFILE: 'https://www.googleapis.com/auth/userinfo.profile'
};

// Gmail OAuth2 endpoints
export const gmailEndpoints = {
  AUTH_URL: 'https://accounts.google.com/o/oauth2/v2/auth',
  TOKEN_URL: 'https://oauth2.googleapis.com/token',
  USERINFO_URL: 'https://www.googleapis.com/oauth2/v2/userinfo',
  GMAIL_API_BASE: 'https://gmail.googleapis.com/gmail/v1'
};
