// API Configuration
export const currentServer = process.env.NEXT_PUBLIC_CURRENT_BACKEND_SERVER

// API Endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  LOGIN: `${currentServer}/users/token/`,
  REFRESH_TOKEN: `${currentServer}/users/token/refresh/`,
  VERIFY_TOKEN: `${currentServer}/users/token/verify/`,
  REGISTER: `${currentServer}/users/register/`,
  SET_PASSWORD: `${currentServer}/users/set-password/`,
  GOOGLE_OAUTH: `${currentServer}/users/google-oauth/`,
  ME: `${currentServer}/users/me/`,

  // Chat endpoints
  CHAT: `${currentServer}/tax-acc-assistant/chat/`,
  CHAT_HISTORY: `${currentServer}/tax-acc-assistant/chat-history/`,
  SEND_MEMO_EMAIL: `${currentServer}/tax-acc-assistant/send-memo-email/`,

  // Xero endpoints
  XERO_CONNECT: `${currentServer}/xero/connect/`,
  XERO_CALLBACK: `${currentServer}/xero/callback/`,
  XERO_VERIFY: `${currentServer}/xero/verify/`,
  XERO_TOKEN: `${currentServer}/xero/token/`,
  XERO_INVOICES: `${currentServer}/xero/invoices/`,
  XERO_CONTACTS: `${currentServer}/xero/contacts/`,
  XERO_ACCOUNTS: `${currentServer}/xero/accounts/`,
  XERO_TAX_RATES: `${currentServer}/xero/tax-rates/`,
  XERO_INVOICE_TYPES: `${currentServer}/xero/invoice-types/`,
  XERO_DISCONNECT: `${currentServer}/xero/disconnect/`,
  XERO_SCAN_INVOICES: `${currentServer}/xero/scan-invoices/`,
  XERO_CREATE_INVOICE: `${currentServer}/xero/create-invoice/`,
  XERO_INVOICE_SCHEMA: `${currentServer}/xero/invoice-schema/`,

  // Payment endpoints
  CREATE_CHECKOUT_SESSION: `${currentServer}/payments/create-checkout-session/`,
  PAYMENT_SUCCESS: `${currentServer}/payments/payment-success/`,
  PURCHASE_HISTORY: `${currentServer}/payments/purchase-history/`,
  CONVERT_PRICES: `${currentServer}/payments/convert-prices/`,

  // Invoice Generator endpoints
  INVOICE_TEMPLATES: `${currentServer}/invoice-generator/templates/`,
  INVOICE_TEMPLATE_DETAIL: `${currentServer}/invoice-generator/templates/`,

  // Company Template endpoints
  COMPANY_TEMPLATES: `${currentServer}/invoice-generator/company-templates/`,
  COMPANY_TEMPLATE_DETAIL: `${currentServer}/invoice-generator/company-templates/`,
  COMPANY_LOGO_UPLOAD: `${currentServer}/invoice-generator/company-templates/upload-logo/`,

  // CSV Processing endpoints
  CSV_UPLOAD: `${currentServer}/invoice-generator/csv-upload/`,
  COLUMN_MAPPING_SUGGESTIONS: `${currentServer}/invoice-generator/column-mapping-suggestions/`,

  // Invoice Generation endpoints
  GENERATE_INVOICES: `${currentServer}/invoice-generator/generate-invoices/`,
  DOWNLOAD_INVOICE: `${currentServer}/invoice-generator/download-invoice/`,
  DOWNLOAD_INVOICES_ZIP: `${currentServer}/invoice-generator/download-invoices-zip/`,

  // Email endpoints for sales invoices
  SEND_SALES_INVOICE_EMAILS: `${currentServer}/invoice-generator/send-invoice-emails/`,

  // MS Graph API endpoints
  MS_GRAPH_AUTH: `${currentServer}/invoice-generator/ms-graph-api/auth/`,
  MS_GRAPH_ACCOUNTS: `${currentServer}/invoice-generator/ms-graph-api/accounts/`,
  MS_GRAPH_SEND_TEST_EMAIL: `${currentServer}/invoice-generator/ms-graph-api/send-test-email/`,
  MS_GRAPH_SAVE_CONFIG: `${currentServer}/invoice-generator/ms-graph-api/save-config/`,

  // Make sure all endpoints end with a slash
};

// API Headers
export const getAuthHeaders = (token) => {
  return {
    'Content-Type': 'application/json',
    Authorization: token ? `Bearer ${token}` : '',
  };
};