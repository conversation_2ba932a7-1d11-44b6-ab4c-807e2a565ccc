import { API_ENDPOINTS, currentServer } from '@/config/api';
import axios from 'axios';
import Cookies from 'js-cookie';

// Ensure HTTPS for production environments
const enforceHTTPS = (url) => {
  if (process.env.NODE_ENV === 'production' && url.startsWith('http:')) {
    return url.replace('http:', 'https:');
  }
  return url;
};

// Create a custom axios instance
const api = axios.create({
  baseURL: enforceHTTPS(currentServer),
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to add the auth token to each request
api.interceptors.request.use(
  (config) => {
    // Ensure HTTPS for API requests in production
    if (process.env.NODE_ENV === 'production' && config.url?.startsWith('http:')) {
      config.url = config.url.replace('http:', 'https:');
    }

    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If error is 401 Unauthorized and not already retrying
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const refreshToken = Cookies.get('refreshToken');
        if (!refreshToken) {
          // If no refresh token, redirect to login
          window.location.href = '/login';
          return Promise.reject(error);
        }

        // Call the refresh token endpoint
        const response = await axios.post(
          enforceHTTPS(API_ENDPOINTS.REFRESH_TOKEN),
          { refresh: refreshToken }
        );

        const { access } = response.data;

        // Update the token
        Cookies.set('token', access, { expires: 1 });

        // Update the header for the original request
        originalRequest.headers.Authorization = `Bearer ${access}`;

        // Retry the original request
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh fails, clear cookies and redirect to login
        Cookies.remove('token');
        Cookies.remove('refreshToken');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Xero API service
export const xeroApi = {
  // Connect to Xero and get the authorization URL
  getAuthorizationUrl: () => api.get(API_ENDPOINTS.XERO_CONNECT),

  // Verify Xero connection status
  verifyConnection: () => api.get(API_ENDPOINTS.XERO_VERIFY),

  // Get Xero token for making direct API calls
  getToken: () => api.get(API_ENDPOINTS.XERO_TOKEN),

  // Get invoices from Xero
  getInvoices: () => api.get(API_ENDPOINTS.XERO_INVOICES),

  // Get contacts from Xero
  getContacts: () => api.get(API_ENDPOINTS.XERO_CONTACTS),

  // Get accounts from Xero
  getAccounts: () => api.get(API_ENDPOINTS.XERO_ACCOUNTS),

  // Get tax rates from Xero
  getTaxRates: () => api.get(API_ENDPOINTS.XERO_TAX_RATES),

  // Get invoice types from Xero
  getInvoiceTypes: () => api.get(API_ENDPOINTS.XERO_INVOICE_TYPES),

  // Disconnect Xero integration
  disconnect: () => api.post(API_ENDPOINTS.XERO_DISCONNECT),

  // Scan PDF invoices with Azure Form Recognizer
  scanInvoices: (formData) => api.post(API_ENDPOINTS.XERO_SCAN_INVOICES, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),

  // Get the invoice schema for validation
  getInvoiceSchema: () => api.get(API_ENDPOINTS.XERO_INVOICE_SCHEMA),

  // Create a new invoice in Xero
  createInvoice: (invoiceData) => api.post(API_ENDPOINTS.XERO_CREATE_INVOICE, invoiceData)
};

// Invoice download service with authentication
export const downloadService = {
  // Download individual invoice with authentication
  downloadInvoice: async (downloadUrl) => {
    try {
      const token = Cookies.get('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Download failed: ${response.status} ${response.statusText}`);
      }

      // Get filename from Content-Disposition header or URL
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'invoice.pdf';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      } else {
        // Extract filename from URL
        const urlParts = downloadUrl.split('/');
        const lastPart = urlParts[urlParts.length - 2]; // Remove trailing slash
        if (lastPart && lastPart.includes('.pdf')) {
          filename = lastPart;
        }
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      return { success: true, filename };
    } catch (error) {
      console.error('Download error:', error);
      throw error;
    }
  },

  // Download ZIP file with authentication
  downloadZip: async (zipUrl, filePaths = null) => {
    try {
      const token = Cookies.get('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      let response;

      if (filePaths && filePaths.length > 0) {
        // Use POST request with specific file paths for current batch
        response = await fetch(zipUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            file_paths: filePaths
          }),
        });
      } else {
        // Use GET request for all invoices (legacy behavior)
        response = await fetch(zipUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      }

      if (!response.ok) {
        throw new Error(`Download failed: ${response.status} ${response.statusText}`);
      }

      // Get filename from Content-Disposition header or use default
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'invoices.zip';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      return { success: true, filename };
    } catch (error) {
      console.error('ZIP download error:', error);
      throw error;
    }
  }
};

export default api;